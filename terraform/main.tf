# ==============================================================================
# PROVIDER CONFIGURATIONS
# ==============================================================================

# Default AWS provider (uses the region from AWS CLI or environment variables)
# This is the primary provider for most resources

# Additional AWS provider for us-east-1 region
# Required for CloudFront ACM certificates which must be in us-east-1
provider "aws" {
  alias  = "us_east_1"
  region = "us-east-1"
}

# ==============================================================================
# LAMBDA FUNCTION AND LAYERS
# ==============================================================================

# tfsec:ignore:aws-lambda-enable-tracing
resource "aws_lambda_function" "function" {
  depends_on    = [aws_lambda_layer_version.layer, aws_lambda_layer_version.templates_layer, aws_lambda_layer_version.configs_layer]
  filename      = "lambda.zip"
  function_name = "${var.project_name}-function"

  role   = aws_iam_role.lambda_role.arn
  layers = [aws_lambda_layer_version.layer.arn, aws_lambda_layer_version.templates_layer.arn, aws_lambda_layer_version.configs_layer.arn]

  handler     = "bootstrap"
  runtime     = "provided.al2"
  timeout     = var.nuclei_timeout
  memory_size = var.memory_size

  source_code_hash = data.archive_file.zip.output_base64sha256

  environment {
    variables = {
      "BUCKET_NAME" = aws_s3_bucket.bucket.id
    }
  }

  tags = var.tags
}

resource "aws_lambda_alias" "alias" {
  name             = var.project_name
  description      = "Nuclei scanner lambda function"
  function_name    = aws_lambda_function.function.arn
  function_version = "$LATEST"
}

# Layer to run nuclei in lambda
resource "aws_lambda_layer_version" "layer" {
  depends_on          = [aws_s3_object.upload_nuclei]
  layer_name          = "${var.project_name}-nuclei-layer"
  s3_bucket           = aws_s3_bucket.bucket.id
  s3_key              = "nuclei.zip"
  compatible_runtimes = ["provided.al2"]
}

# Layer to have nuclei templates
resource "aws_lambda_layer_version" "templates_layer" {
  depends_on          = [aws_s3_object.upload_templates]
  layer_name          = "${var.project_name}-custom-nuclei-templates-layer"
  s3_bucket           = aws_s3_bucket.bucket.id
  s3_key              = "custom-nuclei-templates.zip"
  compatible_runtimes = ["provided.al2"]
}

# Layer for nuclei configs
resource "aws_lambda_layer_version" "configs_layer" {
  depends_on          = [aws_s3_object.upload_config]
  layer_name          = "${var.project_name}-nuclei-config-layer"
  s3_bucket           = aws_s3_bucket.bucket.id
  s3_key              = "nuclei-configs.zip"
  compatible_runtimes = ["provided.al2"]
}

# tfsec:ignore:aws-cloudwatch-log-group-customer-key
resource "aws_cloudwatch_log_group" "log_group" {
  name = "/aws/lambda/${var.project_name}-function"

  retention_in_days = 90

  tags = var.tags
}

###
# IAM
###
resource "aws_iam_role" "lambda_role" {
  name = "${var.project_name}-role"

  assume_role_policy = data.aws_iam_policy_document.trust.json

  tags = var.tags
}

data "aws_iam_policy_document" "trust" {
  statement {
    actions = ["sts:AssumeRole"]

    principals {
      type        = "Service"
      identifiers = ["lambda.amazonaws.com"]
    }
  }
}

# attach policy to role
resource "aws_iam_role_policy_attachment" "policy" {
  role       = aws_iam_role.lambda_role.name
  policy_arn = aws_iam_policy.policy.arn
}

# IAM policy for lambda
resource "aws_iam_policy" "policy" {
  name        = "${var.project_name}-policy"
  description = "Policy for lambda"

  policy = data.aws_iam_policy_document.policy.json
}

# tfsec:ignore:aws-iam-no-policy-wildcards
data "aws_iam_policy_document" "policy" {
  statement {
    sid = "AllowCloudWatchLogs"
    actions = [
      "logs:CreateLogGroup",
      "logs:CreateLogStream",
      "logs:PutLogEvents"
    ]

    resources = ["arn:aws:logs:*:*:*"]
  }

  statement {
    sid    = "AllowS3Upload"
    effect = "Allow"
    actions = [
      "s3:PutObject"
    ]
    resources = [
      "arn:aws:s3:::${aws_s3_bucket.bucket.id}/findings/*"
    ]
  }
}

###
# Shared Network Infrastructure
###
module "network" {
  source = "./network"

  project_name = var.project_name
  tags         = var.tags

  # Network configuration
  vpc_cidr                 = var.vpc_cidr
  public_subnet_az1_cidr   = var.public_subnet_az1_cidr
  public_subnet_az2_cidr   = var.public_subnet_az2_cidr
  private_subnet_az1_cidr  = var.private_subnet_az1_cidr
  private_subnet_az2_cidr  = var.private_subnet_az2_cidr
  enable_nat_gateway_ha    = var.enable_nat_gateway_ha
}

###
# Nuclear Pond Backend Infrastructure
###
module "nuclear_pond_backend" {
  source = "./nuclear_pond_backend"
  count  = var.enable_nuclear_pond_backend ? 1 : 0

  # Common parameters
  project_name = var.project_name
  environment  = var.nuclear_pond_environment
  tags         = var.tags

  # Network configuration
  vpc_id             = module.network.vpc_id
  public_subnet_ids  = module.network.public_subnet_ids
  private_subnet_ids = module.network.private_subnet_ids

  # Application configuration
  api_key                = var.nuclearpond_api_key
  lambda_function_name   = aws_lambda_function.function.function_name
  lambda_function_arn    = aws_lambda_function.function.arn
  dynamodb_table_name    = aws_dynamodb_table.scan_state_table.name
  dynamodb_table_arn     = aws_dynamodb_table.scan_state_table.arn

  # ECS configuration
  task_cpu                = var.nuclear_pond_task_cpu
  task_memory             = var.nuclear_pond_task_memory
  desired_count           = var.nuclear_pond_desired_count
  container_port          = var.nuclear_pond_container_port
  health_check_path       = var.nuclear_pond_health_check_path

  # Monitoring configuration
  log_retention_days      = var.nuclear_pond_log_retention_days

  # Load balancer configuration
  enable_deletion_protection = var.nuclear_pond_enable_deletion_protection
}

###
# Proof of Work (PoW) Target Infrastructure
###
module "pow_targets" {
  source = "./pow"

  # Only create PoW resources if enabled
  enable_pow_targets = var.enable_pow_targets

  # Common parameters
  project_name = var.project_name
  tags         = var.tags

  # Domain configuration
  pow_domain_name = var.pow_domain_name

  # Network configuration - use shared network module
  vpc_id            = module.network.vpc_id
  public_subnet_ids = module.network.public_subnet_ids
}

###
# Frontend Infrastructure
###
module "frontend" {
  source = "./frontend"
  count  = var.enable_frontend_deployment ? 1 : 0

  # Provider configuration for ACM certificate (must be in us-east-1 for CloudFront)
  providers = {
    aws.us_east_1 = aws.us_east_1
  }

  # Common parameters
  project_name = var.project_name
  tags         = var.tags
  environment  = var.frontend_environment

  # Domain configuration
  frontend_domain_name  = var.frontend_domain_name
  create_route53_record = var.create_frontend_route53_record
  route53_zone_id       = var.frontend_route53_zone_id

  # CloudFront configuration
  enable_cloudfront     = var.enable_frontend_cloudfront
  cloudfront_price_class = var.frontend_cloudfront_price_class

  # API configuration
  api_url       = var.enable_nuclear_pond_backend ? module.nuclear_pond_backend[0].alb_dns_name : ""
  api_key       = var.nuclearpond_api_key
  demo_password = var.frontend_demo_password
}